package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 分镜解析请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分镜解析请求")
public class SceneParseRequestDTO {

    @Schema(description = "小说ID")
    @NotNull(message = "小说ID不能为空")
    private Long novelId;

    @Schema(description = "章节ID列表")
    @NotEmpty(message = "章节ID列表不能为空")
    private List<Long> chapterIds;

    @Schema(description = "是否覆盖已存在的分镜", example = "false")
    private Boolean overwrite = false;

    @Schema(description = "是否自动关联角色", example = "true")
    private Boolean autoLinkCharacter = true;

    @Schema(description = "是否生成AI绘画提示词", example = "true")
    private Boolean generatePrompt = true;
}
