package org.example.novel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 分镜音频实体类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneAudio {

    /**
     * 音频ID
     */
    private Long id;

    /**
     * 分镜ID
     */
    private Long sceneId;

    /**
     * 音频文件UUID（MinIO中的文件标识）
     */
    private String audioUuid;

    /**
     * 音频文件名
     */
    private String fileName;

    /**
     * 音频时长（秒）
     */
    private Integer duration;

    /**
     * 音频大小（字节）
     */
    private Long fileSize;

    /**
     * 生成状态：PENDING(等待生成)、GENERATING(生成中)、COMPLETED(已完成)、FAILED(失败)
     */
    private String status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
