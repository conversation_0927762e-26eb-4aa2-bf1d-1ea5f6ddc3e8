package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.NovelCharacter;

import java.util.List;

/**
 * 小说角色数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface NovelCharacterMapper {

    /**
     * 通过ID查询角色
     *
     * @param id 角色ID
     * @return 角色信息
     */
    NovelCharacter findById(@Param("id") Long id);

    /**
     * 根据小说ID查询所有角色
     *
     * @param novelId 小说ID
     * @return 角色列表
     */
    List<NovelCharacter> findByNovelId(@Param("novelId") Long novelId);

    /**
     * 通过小说ID查询角色列表（分页）
     *
     * @param novelId 小说ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 角色列表
     */
    List<NovelCharacter> findByNovelIdWithPaging(@Param("novelId") Long novelId,
                                                 @Param("offset") Integer offset,
                                                 @Param("limit") Integer limit);

    /**
     * 通过小说ID和角色名称查询角色
     *
     * @param novelId 小说ID
     * @param name 角色名称
     * @return 角色信息
     */
    NovelCharacter findByNovelIdAndName(@Param("novelId") Long novelId, @Param("name") String name);

    /**
     * 统计小说的角色数量
     *
     * @param novelId 小说ID
     * @return 角色数量
     */
    Long countByNovelId(@Param("novelId") Long novelId);

    /**
     * 插入角色
     *
     * @param character 角色信息
     * @return 影响行数
     */
    int insert(NovelCharacter character);

    /**
     * 批量插入角色
     *
     * @param characters 角色列表
     * @return 影响行数
     */
    int batchInsert(@Param("characters") List<NovelCharacter> characters);

    /**
     * 更新角色
     *
     * @param character 角色信息
     * @return 影响行数
     */
    int update(NovelCharacter character);

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 删除小说的所有角色
     *
     * @param novelId 小说ID
     * @return 影响行数
     */
    int deleteByNovelId(@Param("novelId") Long novelId);

    /**
     * 检查角色是否属于指定小说
     *
     * @param id 角色ID
     * @param novelId 小说ID
     * @return 是否属于该小说
     */
    boolean existsByIdAndNovelId(@Param("id") Long id, @Param("novelId") Long novelId);

    /**
     * 检查角色名称在小说中是否已存在
     *
     * @param novelId 小说ID
     * @param name 角色名称
     * @param excludeId 排除的角色ID（用于更新时检查）
     * @return 是否已存在
     */
    boolean existsByNovelIdAndName(@Param("novelId") Long novelId, 
                                  @Param("name") String name, 
                                  @Param("excludeId") Long excludeId);
}
