# AI模型配置
ai:
  # Gemini配置
  gemini:
    api-key: ${GEMINI_API_KEY:AIzaSyDxn9J3L9V0qAWYHFm9t8a095iy8MiUhs4}
    base-url: https://gemini.liuyuao.xyz/v1beta
    model: gemini-2.5-flash
    temperature: 0.3
    max-output-tokens: 10000
    timeout: 30s
    # 代理配置
    proxy:
      enabled: ${GEMINI_PROXY_ENABLED:true}
      host: ${GEMINI_PROXY_HOST:127.0.0.1}
      port: ${GEMINI_PROXY_PORT:7890}
    retry:
      max-attempts: 3
      initial-delay: 2s
      multiplier: 2
      max-delay: 30s
  
  # Ollama配置
  ollama:
    base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
    model: ${OLLAMA_MODEL:llama3.2}
    temperature: 0.7
    timeout: 60s
    retry:
      max-attempts: 2
      initial-delay: 1s
      multiplier: 1.5
      max-delay: 10s
  
  # 默认使用的模型类型
  default-provider: gemini  # gemini 或 ollama
