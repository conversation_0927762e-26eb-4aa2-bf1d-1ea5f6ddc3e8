# AI视频生成项目后端任务书

## 项目概述

本项目是一个使用AI绘画技术（Flux）和AI声音克隆（Index TTS）的生成视频项目。后端负责提供API接口、数据库管理、文件存储、AI模型集成等核心功能。

## 技术栈

- 核心框架：Spring Boot 3.4.6
- 安全框架：Spring Security + JWT
- 数据库：MySQL 8.0
- 对象存储：MinIO
- 构建工具：Maven
- 其他：Lombok, MapStruct, Swagger/OpenAPI

## 开发任务分解

### 1. 项目基础搭建

- [√] 创建Spring Boot项目，
- [√] 配置基本依赖
- [√] 搭建项目结构（controller, service, repository, entity等）
- [√] 配置数据库连接（application.yml）
- [√] 配置MinIO连接
- [√] 配置Swagger/OpenAPI文档(http:localhost:8080/api/swagger-ui.html)
- [√] 编写全局异常处理
- [√] 实现统一响应结构

### 2. 用户认证与授权系统

- [√] 实现用户实体类和数据访问层
- [√] 配置Spring Security
- [√] 实现JWT认证机制
- [√] 实现用户注册接口
- [√] 实现用户登录接口
- [√] 实现用户角色权限控制（普通用户/VIP用户）
- [√] 实现用户信息管理接口

### 3. 文件管理系统

- [√] 实现MinIO服务配置与连接
- [√] 实现文件上传功能，支持MD5校验去重
- [√] 实现文件资源实体类和数据访问层
- [√] 实现文件下载功能
- [√] 实现文件预览功能（图片/音频）
- [√] 实现文件删除功能
- [√] 实现不同类型文件的存储桶管理

### 4. 素材库管理

- [√] 实现角色素材实体类和数据访问层
- [√] 实现音频素材实体类和数据访问层
- [√] 实现角色素材CRUD接口
- [√] 实现音频素材CRUD接口
- [√] 实现素材分页查询和筛选功能
- [√] 实现素材标签管理功能

### 5. 小说管理系统

- [√] 实现小说实体类和数据访问层
- [√] 实现小说章节实体类和数据访问层
- [√] 实现小说CRUD接口
- [√] 实现小说章节CRUD接口（小说上传方式为txt文件，可批量上传多章，用正则智能提取小说章节，支持中文数字和阿拉伯数字，在数据库中统一存储为阿拉伯数字，如第13章）
- [√] 实现小说内容存储到MinIO的功能
- [√] 实现小说章节内容读取功能

### 6. 小说角色管理

- [√] 实现小说角色实体类和数据访问层
- [√] 实现大模型API接口集成，用于角色解析
- [√] 实现角色解析功能（异步处理）
- [√] 实现角色与素材库关联功能
- [√] 实现角色管理接口

### 7. 异步任务管理

- [√] 实现异步任务实体类和数据访问层
- [√] 实现任务状态管理和进度跟踪
- [√] 实现任务查询和统计功能
- [√] 实现任务管理接口
- [√] 集成到角色解析功能中

### 8. 小说分镜管理

- [ ] 实现分镜实体类和数据访问层
- [ ] 实现分镜音频实体类和数据访问层
- [ ] 实现分镜图片实体类和数据访问层
- [ ] 实现大模型API接口集成，用于分镜解析
- [ ] 实现分镜解析功能（异步处理）
- [ ] ai 分镜解析应包含content 和 speaker，speaker应与novel_character关联(novel_character表不存在就插入，speaker填主键)
- [ ] 实现分镜管理接口


### 9. AI模型集成

- [ ] 集成Flux AI绘画模型API（异步处理）
- [ ] 集成Index TTS语音克隆模型API（异步处理）
- [ ] 实现分镜音频生成和管理功能（异步处理）
- [ ] 实现分镜图片生成和管理功能（异步处理）
- [ ] 集成异步任务管理
- [√] 集成大模型文本分析API（Gemini + Ollama）
- [√] 实现AI任务队列管理
- [√] 实现异步任务处理机制
- [√] 实现任务状态跟踪和失败重试机制

### 10. 视频生成系统

- [ ] 实现视频项目实体类和数据访问层
- [ ] 实现视频生成任务管理（异步处理）
- [ ] 实现视频合成服务（整合分镜、音频和图片）
- [ ] 实现视频存储到MinIO的功能
- [ ] 实现视频预览和下载功能
- [ ] 集成异步任务管理

### 10. 系统监控与日志

- [ ] 配置日志系统
- [ ] 实现操作日志记录
- [ ] 实现系统监控功能
- [ ] 实现性能指标收集

### 11. 接口文档与测试

- [ ] 编写详细的API文档
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 进行性能测试和优化

## 数据库表结构

已创建的数据库表包括：

1. `user` - 用户表
2. `file_resource` - 文件资源表
3. `role` - 角色素材表
4. `audio` - 音频素材表
5. `novel` - 小说表
6. `novel_chapter` - 小说章节表
7. `novel_character` - 小说角色表
8. `chapter_scene` - 章节分镜表
9. `scene_audio` - 分镜音频表
10. `scene_image` - 分镜图片表
11. `video_project` - 视频项目表
12. `async_task` - 异步任务表

## 接口规范

### 请求格式

- GET 请求：用于查询操作
- POST 请求：用于创建操作
- PUT 请求：用于更新操作
- DELETE 请求：用于删除操作

### 响应格式

统一的JSON响应结构：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### 错误处理

统一的错误响应结构：

```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

## 开发规范

1. **代码风格**：
   - 遵循阿里巴巴Java开发手册
   - 使用Lombok简化代码
   - 使用MapStruct进行对象映射

2. **命名规范**：
   - 类名：大驼峰命名法（PascalCase）
   - 方法名、变量名：小驼峰命名法（camelCase）
   - 常量名：全大写下划线分隔（SNAKE_CASE）

3. **注释规范**：
   - 类注释：说明类的用途、作者、日期
   - 方法注释：说明方法的功能、参数、返回值、异常

4. **异常处理**：
   - 使用自定义异常类
   - 统一异常处理器捕获并转换为友好响应

5. **日志规范**：
   - 使用SLF4J + Logback
   - 不同级别日志的合理使用

## 开发进度安排

1. **第一阶段（2周）**：
   - 项目基础搭建
   - 用户认证与授权系统
   - 文件管理系统

2. **第二阶段（2周）**：
   - 素材库管理
   - 小说管理系统
   - 小说角色管理

3. **第三阶段（3周）**：
   - 小说分镜管理
   - AI模型集成
   - 视频生成系统

4. **第四阶段（1周）**：
   - 系统监控与日志
   - 接口文档与测试
   - 系统优化与上线准备

## 技术难点与解决方案

1. **文件MD5去重**：
   - 前端计算MD5后先查询是否存在
   - 后端二次校验MD5，实现秒传功能

2. **MinIO文件管理**：
   - 使用不同bucket分类存储
   - 实现访问权限控制
   - 生成临时访问URL

3. **AI模型集成**：
   - 使用异步任务处理耗时操作
   - 实现任务队列和状态管理
   - 失败重试机制

4. **分布式任务处理**：
   - 考虑使用消息队列（RabbitMQ/Kafka）
   - 实现任务分发和结果回调

5. **性能优化**：
   - 合理使用缓存
   - 数据库查询优化
   - 大文件分片上传

## 测试计划

1. **单元测试**：
   - 使用JUnit + Mockito
   - 覆盖核心业务逻辑

2. **集成测试**：
   - 测试API接口
   - 测试数据库交互
   - 测试外部系统集成

3. **性能测试**：
   - 使用JMeter测试接口性能
   - 模拟高并发场景
   - 测试文件上传下载性能

## 部署方案

1. **开发环境**：
   - 本地开发环境
   - 开发服务器

2. **测试环境**：
   - 测试服务器
   - CI/CD自动部署

3. **生产环境**：
   - 使用Docker容器化部署
   - 配置负载均衡
   - 实现高可用方案

## 异步处理设计原则

### 耗时任务异步化
所有可能耗时超过3秒的任务都必须采用异步处理模式：

1. **AI模型调用**：
   - 角色解析（CHARACTER_PARSE）
   - 场景解析（SCENE_PARSE）
   - 分镜生成（STORYBOARD_GENERATE）
   - 图片生成（IMAGE_GENERATE）
   - 音频生成（AUDIO_GENERATE）

2. **文件处理**：
   - 大文件上传处理
   - 批量文件转换
   - 视频合成（VIDEO_GENERATE）

3. **数据处理**：
   - 批量数据导入/导出
   - 复杂数据分析

### 异步任务标准流程
1. **任务创建**：生成唯一taskId，立即返回给用户
2. **状态跟踪**：实时更新任务进度和状态
3. **结果查询**：用户可随时查询任务状态和结果
4. **错误处理**：详细记录错误信息，支持重试
5. **资源清理**：定时清理过期任务数据

### 用户体验优化
- 用户无需等待，可继续其他操作
- 提供实时进度反馈
- 支持任务列表查看和管理
- 任务完成后可选择通知用户
