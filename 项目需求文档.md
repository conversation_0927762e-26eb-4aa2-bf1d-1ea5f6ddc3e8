
# AI视频生成项目需求文档

## 项目概述
本项目是一个使用AI绘画技术加AI声音克隆的生成视频项目（分别采用Flux和Index TTS）

## 技术栈
- 后端技术：SpringBoot + Spring Security + MySQL + MinIO
- 前端技术：Vite + Vue3 + Element Plus + Pinia

## 系统功能与数据库设计

### 1. 用户管理
- 采用JWT验证，Spring Security鉴权
- 用户角色：普通用户和VIP用户
- 数据表设计：
  ```sql
  CREATE TABLE `user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码（加密存储）',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `role` varchar(20) NOT NULL DEFAULT 'USER' COMMENT '角色：USER-普通用户，VIP-VIP用户',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
  ```

### 2. 文件管理
- 文件上传到MinIO，实现MD5去重，返回UUID
- 数据表设计：
  ```sql
  CREATE TABLE `file_resource` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '资源ID',
    `uuid` varchar(36) NOT NULL COMMENT 'MinIO文件UUID',
    `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
    `md5` varchar(32) NOT NULL COMMENT '文件MD5值',
    `size` bigint NOT NULL COMMENT '文件大小(字节)',
    `mime_type` varchar(100) NOT NULL COMMENT '文件MIME类型',
    `bucket` varchar(50) NOT NULL COMMENT 'MinIO存储桶',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_md5` (`md5`),
    UNIQUE KEY `uk_uuid` (`uuid`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件资源表';
  ```

### 3. 素材库管理

#### 3.1 角色素材库
- 用于AI绘画的角色参考，确保出图人物一致性
- 包含角色名称、角色prompt、角色图片
- 数据表设计：
  ```sql
  CREATE TABLE `role` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `name` varchar(100) NOT NULL COMMENT '角色名称',
    `prompt` text NOT NULL COMMENT '角色Prompt描述',
    `image_uuid` varchar(36) NOT NULL COMMENT '角色图片MinIO的UUID',
    `tags` varchar(255) DEFAULT NULL COMMENT '角色标签，多个标签用逗号分隔',
    `user_id` bigint NOT NULL COMMENT '创建用户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色素材表';
  ```

#### 3.2 音频素材库
- 用于Index TTS参考音频
- 数据表设计：
  ```sql
  CREATE TABLE `audio` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '音频ID',
    `name` varchar(100) NOT NULL COMMENT '音频名称',
    `description` varchar(255) DEFAULT NULL COMMENT '音频描述',
    `audio_uuid` varchar(36) NOT NULL COMMENT '音频文件MinIO的UUID',
    `duration` int NOT NULL COMMENT '音频时长(秒)',
    `voice_type` varchar(50) DEFAULT NULL COMMENT '音色类型',
    `user_id` bigint NOT NULL COMMENT '创建用户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音频素材表';
  ```

### 4. 小说管理
- 使用Python爬虫爬取整本小说
- 小说内容存储方案：章节内容存储在MinIO中，避免MySQL存储大文本导致性能问题
- 数据表设计：
  ```sql
  CREATE TABLE `novel` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '小说ID',
    `title` varchar(200) NOT NULL COMMENT '小说名称',
    `author` varchar(100) NOT NULL COMMENT '作者',
    `cover_uuid` varchar(36) DEFAULT NULL COMMENT '封面图片MinIO的UUID',
    `description` text DEFAULT NULL COMMENT '小说简介',
    `source_url` varchar(500) DEFAULT NULL COMMENT '来源URL',
    `chapter_count` int DEFAULT 0 COMMENT '章节数量',
    `user_id` bigint NOT NULL COMMENT '创建用户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小说表';
  
  CREATE TABLE `novel_chapter` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '章节ID',
    `novel_id` bigint NOT NULL COMMENT '小说ID',
    `title` varchar(200) NOT NULL COMMENT '章节标题',
    `chapter_index` int NOT NULL COMMENT '章节序号',
    `content_uuid` varchar(36) NOT NULL COMMENT '章节内容MinIO的UUID',
    `word_count` int DEFAULT 0 COMMENT '字数',
    `is_scene_parsed` tinyint(1) DEFAULT 0 COMMENT '是否已进行分镜解析',
    `is_character_parsed` tinyint(1) DEFAULT 0 COMMENT '是否已解析角色',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_novel_id` (`novel_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小说章节表';
  ```

### 5. 小说角色管理
- 解析小说中的角色，并关联到素材库
- 数据表设计：
  ```sql
  CREATE TABLE `novel_character` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '小说角色ID',
    `novel_id` bigint NOT NULL COMMENT '小说ID',
    `name` varchar(100) NOT NULL COMMENT '角色名称',
    `description` text DEFAULT NULL COMMENT '角色描述',
    `role_id` bigint DEFAULT NULL COMMENT '关联的角色素材ID',
    `audio_id` bigint DEFAULT NULL COMMENT '关联的音频素材ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_novel_name` (`novel_id`, `name`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_audio_id` (`audio_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小说角色表';
  ```

### 6. 异步任务管理
- 管理系统中的耗时异步任务（角色解析、场景解析、视频生成等）
- 提供任务状态查询和进度跟踪功能
- 数据表设计：
  ```sql
  CREATE TABLE `async_task` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id` varchar(100) NOT NULL COMMENT '任务唯一标识',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `task_type` varchar(50) NOT NULL COMMENT '任务类型：CHARACTER_PARSE(角色解析)、SCENE_PARSE(场景解析)、VIDEO_GENERATE(视频生成)等',
    `task_name` varchar(200) NOT NULL COMMENT '任务名称/描述',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态：PENDING(等待中)、RUNNING(进行中)、COMPLETED(已完成)、FAILED(失败)、CANCELLED(已取消)',
    `progress` int NOT NULL DEFAULT 0 COMMENT '进度百分比(0-100)',
    `current_step` varchar(500) DEFAULT NULL COMMENT '当前执行步骤描述',
    `request_data` json DEFAULT NULL COMMENT '请求参数数据',
    `result_data` json DEFAULT NULL COMMENT '任务结果数据',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `duration` bigint DEFAULT NULL COMMENT '执行耗时(毫秒)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_id` (`task_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_task_type` (`task_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异步任务表';
  ```

### 8. 小说分镜管理
- 使用AI智能将小说章节划分为多个分镜片段
- 每个分镜包含内容、说话者和AI绘画提示词
- 支持自动关联说话者到角色表
- 数据表设计：
  ```sql
  CREATE TABLE `chapter_scene` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分镜ID',
    `chapter_id` bigint NOT NULL COMMENT '章节ID',
    `scene_index` int NOT NULL COMMENT '分镜序号',
    `content` text NOT NULL COMMENT '分镜文本内容',
    `prompt` text DEFAULT NULL COMMENT 'AI绘画提示词',
    `speaker` varchar(100) DEFAULT NULL COMMENT '朗读者/说话者（null表示旁白）',
    `character_id` bigint DEFAULT NULL COMMENT '关联的小说角色ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_chapter_id` (`chapter_id`),
    KEY `idx_character_id` (`character_id`),
    KEY `idx_speaker` (`speaker`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='章节分镜表';

  CREATE TABLE `scene_audio` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '音频ID',
    `scene_id` bigint NOT NULL COMMENT '分镜ID',
    `audio_uuid` varchar(100) NOT NULL COMMENT '音频文件UUID',
    `file_name` varchar(255) NOT NULL COMMENT '音频文件名',
    `duration` int DEFAULT NULL COMMENT '音频时长（秒）',
    `file_size` bigint DEFAULT NULL COMMENT '音频大小（字节）',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '生成状态',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_scene_id` (`scene_id`),
    KEY `idx_status` (`status`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分镜音频表';

  CREATE TABLE `scene_image` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '图片ID',
    `scene_id` bigint NOT NULL COMMENT '分镜ID',
    `image_uuid` varchar(100) NOT NULL COMMENT '图片文件UUID',
    `file_name` varchar(255) NOT NULL COMMENT '图片文件名',
    `width` int DEFAULT NULL COMMENT '图片宽度',
    `height` int DEFAULT NULL COMMENT '图片高度',
    `file_size` bigint DEFAULT NULL COMMENT '图片大小（字节）',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '生成状态',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_scene_id` (`scene_id`),
    KEY `idx_status` (`status`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分镜图片表';
  ```
- 使用大模型对章节进行分镜处理
- 每个分镜关联朗读角色、多段音频和AI生成的图片
- 数据表设计：
  ```sql
  CREATE TABLE `chapter_scene` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分镜ID',
    `chapter_id` bigint NOT NULL COMMENT '章节ID',
    `scene_index` int NOT NULL COMMENT '分镜序号',
    `content` text NOT NULL COMMENT '分镜文本内容',
    `prompt` text NOT NULL COMMENT '分镜ai绘画提示词',
    `speaker` varchar(100) DEFAULT '旁白' COMMENT '朗读者/说话者',
    `character_id` bigint DEFAULT NULL COMMENT '关联的小说角色ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_chapter_id` (`chapter_id`),
    KEY `idx_character_id` (`character_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='章节分镜表';
  
  CREATE TABLE `scene_audio` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `scene_id` bigint NOT NULL COMMENT '分镜ID',
    `audio_uuid` varchar(36) NOT NULL COMMENT '生成的音频UUID',
    `duration` int NOT NULL COMMENT '音频时长(秒)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_scene_id` (`scene_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分镜音频表';
  
  CREATE TABLE `scene_image` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `scene_id` bigint NOT NULL COMMENT '分镜ID',
    `image_uuid` varchar(36) NOT NULL COMMENT '生成的图片UUID',
    `prompt` text NOT NULL COMMENT '生成图片使用的Prompt',
    `is_selected` tinyint(1) DEFAULT 0 COMMENT '是否被选中作为最终图片',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_scene_id` (`scene_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分镜图片表';
  ```

### 9. 视频生成管理
- 整合分镜、音频和图片生成最终视频
- 数据表设计：
  ```sql
  CREATE TABLE `video_project` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '视频项目ID',
    `name` varchar(200) NOT NULL COMMENT '项目名称',
    `novel_id` bigint NOT NULL COMMENT '关联小说ID',
    `chapter_id` bigint DEFAULT NULL COMMENT '关联章节ID，NULL表示整本小说',
    `status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态：DRAFT-草稿，PROCESSING-处理中，COMPLETED-已完成，FAILED-失败',
    `video_uuid` varchar(36) DEFAULT NULL COMMENT '生成视频MinIO的UUID',
    `duration` int DEFAULT NULL COMMENT '视频时长(秒)',
    `resolution` varchar(20) DEFAULT '720p' COMMENT '视频分辨率',
    `user_id` bigint NOT NULL COMMENT '创建用户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_novel_id` (`novel_id`),
    KEY `idx_user_id` (`user_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频项目表';
  ```

## 系统流程

### 1. 文件上传流程
1. 前端计算文件MD5
2. 发送MD5到后端检查是否已存在
3. 如存在，直接返回对应UUID
4. 如不存在，上传文件到MinIO，生成UUID并记录到数据库

### 2. 小说处理流程
1. 用户上传或爬取小说
2. 系统解析小说章节，存储到MinIO
3. 用户请求解析小说角色
4. 系统使用大模型解析角色，存储到novel_character表
5. 用户关联角色与素材库中的角色卡和音频

### 3. 分镜处理流程
1. 用户选择需要进行分镜处理的章节
2. 系统使用大模型对章节内容进行分镜划分，识别对话和旁白
3. 系统根据分镜内容和关联的角色生成音频
4. 系统为每个分镜生成4张AI图片
5. 用户从生成的图片中选择最佳的一张

### 4. 视频生成流程
1. 用户创建视频项目
2. 系统整合选定的分镜、音频和图片
3. 系统生成最终视频文件
4. 用户可预览和下载生成的视频

## 技术实现要点

### 1. MinIO文件存储
- 使用不同的bucket存储不同类型文件：images、audios、texts、videos
- 实现基于MD5的文件去重机制
- 文件访问权限控制

### 2. AI模型集成
- Flux AI绘画模型集成
- Index TTS语音克隆模型集成
- 大模型文本分析接口

### 3. 分布式任务处理
- 使用消息队列处理耗时的AI生成任务
- 任务状态跟踪和失败重试机制

### 4. 前端界面
- 素材库管理界面
- 小说管理和角色关联界面
- 分镜编辑和预览界面
- 视频项目管理和预览界面

## 扩展功能（未来计划）
1. 更多用户角色和权限管理
2. 素材库共享和市场功能
3. 更多AI模型的集成
4. 视频编辑和后期处理功能
5. 社区分享功能

